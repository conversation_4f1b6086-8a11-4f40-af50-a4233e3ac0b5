<div class="container">
  <h1>Golf Slot Entry</h1>

  <form (submit)="addSlot(); $event.preventDefault();">
    <input placeholder="Tid (e.g. 14:00)" [(ngModel)]="newSlot.tid" name="tid" required>
    <input placeholder="Platser (e.g. 3/4)" [(ngModel)]="newSlot.platser" name="platser" required>
    <input placeholder="Status (Öppen / Fullbokad)" [(ngModel)]="newSlot.status" name="status" required>
    <button type="submit">Add Slot</button>
  </form>

  <table>
    <thead>
      <tr>
        <th>Tid</th>
        <th>Platser</th>
        <th>Status</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let slot of golfSlots">
        <td>{{ slot.tid }}</td>
        <td>{{ slot.platser }}</td>
        <td [ngClass]="{'fullbokad': slot.status === 'Fullbokad', 'oppen': slot.status === 'Öppen'}">
          {{ slot.status }}
        </td>
      </tr>
    </tbody>
  </table>
</div>
