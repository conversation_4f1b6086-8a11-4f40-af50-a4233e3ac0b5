import { Component } from '@angular/core';
import { GolfSlot } from './models/slot.model';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  golfSlots: GolfSlot[] = [];
  newSlot: GolfSlot = { tid: '', platser: '', status: '' };

  addSlot(): void {
    if (this.newSlot.tid && this.newSlot.platser && this.newSlot.status) {
      this.golfSlots.push({ ...this.newSlot });
      this.newSlot = { tid: '', platser: '', status: '' };
    }
  }
}
