// Slot Model Interface
export interface Slot {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Optional: Slot creation interface (without auto-generated fields)
export interface CreateSlot {
  name: string;
  description?: string;
  isActive?: boolean;
}

// Optional: Slot update interface (all fields optional except id)
export interface UpdateSlot {
  id: string;
  name?: string;
  description?: string;
  isActive?: boolean;
}
